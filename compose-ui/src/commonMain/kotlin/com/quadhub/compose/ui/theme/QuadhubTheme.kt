package com.quadhub.compose.ui.theme

import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.quadhub.compose.ui.theme.internal.ClauseStyleColors
import org.jetbrains.compose.resources.Font
import quadhubkmp.compose_ui.generated.resources.Res
import quadhubkmp.compose_ui.generated.resources.inter_bold
import quadhubkmp.compose_ui.generated.resources.inter_medium
import quadhubkmp.compose_ui.generated.resources.inter_regular


@Composable
fun QuadhubTheme(
    invertedColors: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {

    val quadhubColor = Colors(
        surface = Surface(
            primaryDark = QuadhubColors.Black,
            primaryLight = QuadhubColors.White,
            secondaryDark = QuadhubColors.Black,
            secondaryLight = QuadhubColors.White,
        ),
        surfaceInteractive = SurfaceInteractive(
            enabledOnDark = QuadhubColors.White,
            enabledOnLight = QuadhubColors.Black,
            disabledOnDark = QuadhubColors.Secondary.NeutralSix,
            disabledOnLight = QuadhubColors.Secondary.NeutralSix,
            hoverTapOnDark = QuadhubColors.Secondary.NeutralFour,
            hoverTapOnLight = QuadhubColors.Secondary.NeutralFour
        ),
        border = Border(
            primaryOnDark = QuadhubColors.Border.Default,
            primaryOnLight = QuadhubColors.Border.Default,
            secondaryOnDark = QuadhubColors.Secondary.NeutralSix,
            secondaryOnLight = QuadhubColors.Secondary.NeutralSix,
        ),
        element = Element(
            primaryOnDark = QuadhubColors.White,
            primaryOnLight = QuadhubColors.Black,
            secondaryOnDark = QuadhubColors.White,
            secondaryOnLight = QuadhubColors.Black,
        ),
        elementInteractive = ElementInteractive(
            enabledOnDark = QuadhubColors.White,
            enabledOnLight = QuadhubColors.Primary.Blue,
            disabledOnDark = QuadhubColors.White.copy(alpha = 0.1f),
            disabledOnLight = QuadhubColors.Secondary.NeutralSix,
            hoverTapOnDark = QuadhubColors.White.copy(alpha = 0.6f),
            hoverTapOnLight = QuadhubColors.Primary.Blue.copy(alpha = 0.8f)
        ),
        shadow = Shadow(
            spotOnDark = QuadhubColors.Shadow.lightSpot,
            spotOnLight = QuadhubColors.Shadow.darkSpot
        ),
    )

    val interFontFamily = FontFamily(
        Font(Res.font.inter_regular, FontWeight.Normal),
        Font(Res.font.inter_medium, FontWeight.Medium),
        Font(Res.font.inter_bold, FontWeight.Bold)
    )

    val typography = Typography(
        headingSub = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 12.sp,
            color = Color(0xFF525252)
        ),
        headingS = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Medium,
            fontSize = 16.sp,
            color = quadhubColor.element.primaryOnLight
        ),
        headingM = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 18.sp,
            color = quadhubColor.element.primaryOnLight
        ),
        headingL = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 24.sp,
            color = quadhubColor.element.primaryOnLight
        ),
        bodyXs = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 12.sp,
            color = quadhubColor.element.primaryOnLight
        ),
        bodyS = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 14.sp,
            lineHeight = 20.sp,
            color = quadhubColor.element.primaryOnLight
        ),
        body = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Normal,
            fontSize = 16.sp,
            color = quadhubColor.element.primaryOnLight,
        ),
        labelXs = TextStyle(
            fontFamily = interFontFamily,
            fontWeight = FontWeight.Medium,
            fontSize = 10.sp,
            color = quadhubColor.element.primaryOnLight
        )
    )


    val shape = Shape(
        buttonShape = RoundedCornerShape(8.dp),
        circleShape = RoundedCornerShape(50),
        roundShape = RoundedCornerShape(12.dp),
        imageShape = CircleShape,
        card = RoundedCornerShape(8.dp),
        textShape = RoundedCornerShape(4.dp),
        chipShape = RoundedCornerShape(8.dp),
    )
    
    WithTheme(
        colors = quadhubColor,
        typography = typography,
        content = content,
        shape = shape,
        clauseStyleColors = provideClauseColors(invertedColors),
    )

}

/**
 * Enforce the light mode
 */
@Composable
fun isSystemInDarkTheme() = false


@Composable
private fun provideClauseColors(invertedColors: Boolean) = when (invertedColors) {
    true -> ClauseStyleColors(
        regular = QuadhubColors.Secondary.NeutralOne,
        caution = QuadhubColors.Secondary.NeutralFour,
        success = QuadhubColors.Primary.Black,
        error = QuadhubColors.Destructive
    )

    false -> ClauseStyleColors(
        regular = QuadhubColors.Secondary.NeutralOne,
        caution = QuadhubColors.Secondary.NeutralFour,
        success = QuadhubColors.Primary.Black,
        error = QuadhubColors.Destructive
    )
}