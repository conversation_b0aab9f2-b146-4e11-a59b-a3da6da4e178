package com.quadhub.compose.ui.element.text.internal

import androidx.compose.runtime.Composable
import com.quadhub.compose.ui.element.text.Text
import org.jetbrains.compose.resources.stringResource


@Composable
internal fun Text.toText(): String {
    return when (val text = this) {
        is Text.Localized -> stringResource(text.resId, *text.args.map {
            when (it) {
                is Text -> it.toText()
                else -> it
            }
        }.toTypedArray())
        is Text.Simple -> text.text
        is Text.Timestamp -> LocalDateTimeParserFactory.current.format(
            time = text.date,
            format = text.format,
            timeZone = text.timeZone
        )
    }
}