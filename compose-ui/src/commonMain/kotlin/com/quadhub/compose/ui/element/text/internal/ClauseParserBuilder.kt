package com.quadhub.compose.ui.element.text.internal

import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import com.quadhub.compose.ui.element.text.Clause
import com.quadhub.compose.ui.element.text.CompositeClause
import com.quadhub.compose.ui.element.text.Graphics
import com.quadhub.compose.ui.element.text.Text
import com.quadhub.compose.ui.element.text.plus

internal object ClauseParserBuilder {

    @Composable
    fun build(clause: Clause, style: TextStyle): AnnotatedString {
        return when (clause) {
            is CompositeClause -> clause.build(style = style)
            is Graphics -> AnnotatedString("")
            is Text -> AnnotatedString(clause.toText())
        }
    }

    @Composable
    private fun CompositeClause.build(style: TextStyle) = buildAnnotatedString {
        items.forEachIndexed { index, clause ->
            withStyle(style = SpanStyle()) {
                val spanStyle = style.toSpanStyle() + clause.clauseTextStyle
                withStyle(style = spanStyle) {
                    when (val clause = clause) {
                        is Text -> {
                            val currentLinkHandler = LocalTextLinkHandlerProvider.current
                            when (val textId = clause.textActionId) {
                                is String -> withLink(
                                    link = LinkAnnotation.Clickable(
                                        tag = textId,
                                        linkInteractionListener = { currentLinkHandler.onLinkClick?.invoke(textId) },
                                        styles = TextLinkStyles(style = spanStyle.copy(textDecoration = TextDecoration.Underline))
                                    )
                                ) {
                                    append(clause.toText())
                                }
                                else -> append(clause.toText())
                            }
                        }
                        is Graphics -> {
                            val currentLinkHandler = LocalTextLinkHandlerProvider.current
                            when (val actionId = clause.actionId) {
                                is String -> {
                                    withLink(
                                        link = LinkAnnotation.Clickable(
                                            tag = actionId,
                                            linkInteractionListener = { currentLinkHandler.onGraphicsClick?.invoke(actionId) })
                                    ) {
                                        appendInlineContent(id = index.toString())
                                    }
                                }
                                else -> appendInlineContent(id = index.toString())
                            }
                        }
                        is CompositeClause -> throw IllegalArgumentException("Composite clause style cannot be nested")
                    }
                }
            }
        }
    }


}