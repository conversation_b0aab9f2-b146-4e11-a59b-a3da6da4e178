package com.quadhub.compose.ui.element.text

import com.quadhub.compose.ui.element.image.Image
import kotlinx.datetime.TimeZone
import org.jetbrains.compose.resources.StringResource


sealed class Clause {
    internal val clauseTextStyle: ClauseStyle?
        get() = when (this) {
            is Text -> this.clauseStyle
            is CompositeClause, is Graphics -> null
        }
}

data class Graphics(
    val image: Image,
    val style: ClauseStyle? = null,
    val actionId: String? = null,
    val size: Int? = null,
    val blend: Boolean = false
) : Clause()

sealed class Text(open val clauseStyle: ClauseStyle? = null, open val textActionId: String? = null) :
    Clause() {
    data class Localized(
        val resId: StringResource,
        val args: List<Any> = emptyList(),
        override val textActionId: String? = null,
        override val clauseStyle: ClauseStyle? = null
    ) : Text()

    data class Simple(
        val text: String, override val clauseStyle: ClauseStyle? = null,
        override val textActionId: String? = null,
    ) : Text()

    data class Timestamp(
        val date: Long,
        val format: String? = null,
        override val textActionId: String? = null,
        val timeZone: TimeZone? = null,
        override val clauseStyle: ClauseStyle? = null
    ) : Text()

    companion object {
        val empty = Simple("")
        val whiteSpace = Simple(" ")
        val newLine = Simple("\n")

        fun listOf(vararg items: Clause): Clause {
            return CompositeClause(items.toList())
        }
    }
}

/**
 * Makes a text clickable
 */
fun Text.asClickable(textId: String) = when(this) {
    is Text.Localized -> this.copy(textActionId = textId)
    is Text.Simple -> this.copy(textActionId = textId)
    is Text.Timestamp -> this.copy(textActionId = textId)
}

data class CompositeClause(val items: List<Clause>) : Clause()

fun localizedClause(
    resId: StringResource,
    args: List<Any> = emptyList(),
    clauseStyle: ClauseStyle? = null
) = Text.Localized(resId = resId, args = args, clauseStyle = clauseStyle)

fun String.asText(clauseStyle: ClauseStyle? = null, textActionId: String? = null): Text = Text.Simple(text = this, clauseStyle, textActionId = textActionId)
