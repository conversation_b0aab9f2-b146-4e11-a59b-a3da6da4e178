package com.quadhub.compose.ui.component.navbar

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.quadhub.compose.ui.component.navbar.model.TopNavBarModel
import com.quadhub.compose.ui.element.image.Icon
import com.quadhub.compose.ui.element.image.OptionalButtonCallback
import com.quadhub.compose.ui.theme.Theme

private const val NAV_BAR_HEIGHT_DP = 48

@Composable
fun TopNavBar(
    model: TopNavBarModel,
    modifier: Modifier = Modifier,
    onLeadingIconClick: OptionalButtonCallback,
    trailingContext: ((@Composable () -> Unit))? = null,
) {

    when (model) {
        is TopNavBarModel.Default -> TopNavBarDefault(
            modifier = modifier ,
            model = model,
            onLeadingIconClick = onLeadingIconClick,
        )
    }
}

@Composable
private fun TopNavBarDefault(
    modifier: Modifier = Modifier,
    model: TopNavBarModel.Default,
    onLeadingIconClick: OptionalButtonCallback
) {
    Box(
        contentAlignment = Alignment.CenterStart,
        modifier = modifier.height(NAV_BAR_HEIGHT_DP.dp)
            .padding(horizontal = Theme.spacing.x4),
    ){
        Icon(
            image = model.leadingIcon,
            contentDescription = "back",
            modifier = Modifier.size(24.dp),
            onClick = onLeadingIconClick,
        )
    }
}