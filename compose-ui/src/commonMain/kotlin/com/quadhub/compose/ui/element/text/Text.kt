package com.quadhub.compose.ui.element.text

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import com.quadhub.compose.ui.element.text.internal.BaseText
import com.quadhub.compose.ui.element.text.internal.LocalTextLinkHandlerProvider
import com.quadhub.compose.ui.element.text.internal.TextLinkHandler
import com.quadhub.compose.ui.theme.Theme


@Composable
fun Text(
    clause: Clause,
    modifier: Modifier = Modifier,
    style: TextStyle = Theme.typography.body,
    maxLines: Int? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
) {
    when (clause) {
        is Text -> BaseText(
            clause = CompositeClause(listOf(clause)),
            style = style,
            modifier = modifier,
            lineHeight = lineHeight,
            overflow = overflow,
            maxLines = maxLines,
        )
        is CompositeClause -> BaseText(
            clause = clause,
            style = style,
            modifier = modifier,
            lineHeight = lineHeight,
            overflow = overflow,
            maxLines = maxLines,
        )
        is Graphics -> BaseText(
            clause = CompositeClause(listOf(clause)),
            style = style,
            modifier = modifier,
            lineHeight = lineHeight,
            overflow = overflow,
            maxLines = maxLines,
        )
    }
}

@Composable
fun WithTextClickHandler(
    onGraphicsClick: ((tag: String) -> Unit)? = null,
    onTextActionClick: ((tag: String) -> Unit)? = null,
    content: @Composable () -> Unit
) {
    CompositionLocalProvider(
        LocalTextLinkHandlerProvider provides TextLinkHandler(
            onGraphicsClick = onGraphicsClick,
            onLinkClick = onTextActionClick
        ), content = content
    )
}
