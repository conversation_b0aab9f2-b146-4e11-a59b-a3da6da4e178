package com.quadhub.compose.ui.element.text.internal

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import com.quadhub.core.datetime.DateTimeParser

@Composable
internal fun WithDateFormatterProvider(
    datetimeParser: DateTimeParser,
    content: @Composable () -> Unit
) = CompositionLocalProvider(
    LocalDateTimeParserFactory provides datetimeParser,
    content = content
)

internal val LocalDateTimeParserFactory = staticCompositionLocalOf { DateTimeParser.create() }

