package com.quadhub.compose.ui.component.tray.base


import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.quadhub.compose.ui.element.image.OptionalButtonCallback
import com.quadhub.compose.ui.theme.Theme
import com.quadhub.compose.ui.theme.isSystemInDarkTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun BaseModelBottomSheet(
    modifier: Modifier = Modifier,
    sheetState: SheetState,
    onDismissRequest: OptionalButtonCallback,
    invertedColors: Boolean = isSystemInDarkTheme(),
    content: @Composable ColumnScope.() -> Unit,
) {

    ModalBottomSheet(
        containerColor = componentColors(invertedColors = invertedColors),
        onDismissRequest = {
            onDismissRequest?.invoke()
        },
        sheetState = sheetState,
        modifier = modifier,
        contentWindowInsets = { WindowInsets(0.dp) }
    ) {
        content()
    }
}

@Composable
private fun componentColors(invertedColors: Boolean) = when(invertedColors) {
    true -> Theme.colors.surface.primaryDark
    false -> Theme.colors.surface.primaryLight
}