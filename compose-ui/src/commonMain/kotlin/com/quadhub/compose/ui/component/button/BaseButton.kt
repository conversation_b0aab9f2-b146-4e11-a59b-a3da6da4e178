package com.quadhub.compose.ui.component.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.size
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.dp
import com.quadhub.compose.ui.component.button.internals.ButtonColors
import com.quadhub.compose.ui.component.button.internals.LocalButtonColorProvider
import com.quadhub.compose.ui.component.button.model.ButtonModel
import com.quadhub.compose.ui.component.button.model.ButtonState
import com.quadhub.compose.ui.component.button.model.ButtonStyle
import com.quadhub.compose.ui.component.button.model.toTextStyle
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.image.OptionalButtonCallback
import com.quadhub.compose.ui.element.text.Text
import com.quadhub.compose.ui.theme.QuadhubColors
import com.quadhub.compose.ui.theme.Theme


@Composable
internal fun BaseButton(
    model: ButtonModel,
    modifier: Modifier = Modifier,
    style: ButtonStyle,
    state: ButtonState,
    onClick: OptionalButtonCallback,
) {
    val isEnabled = remember(state) { state == ButtonState.Enabled }
    Button(
        onClick = { onClick?.invoke() },
        modifier = modifier,
        shape = Theme.shape.buttonShape,
        border = BorderStroke(
            width = 1.dp,
            color = LocalButtonColorProvider.current.borderOutlineColor
        ),
        colors = LocalButtonColorProvider.current.toButtonColors(),
        contentPadding = PaddingValues(Theme.spacing.x3),
        enabled = isEnabled,
        elevation = ButtonDefaults.elevation(
            defaultElevation = 0.5.dp,
            pressedElevation = 1.0.dp,
            hoveredElevation = 1.5.dp,
            focusedElevation = 1.5.dp
        )
    ) {
        when (state) {
            ButtonState.Loading -> ButtonProgressBar(
                buttonColor = QuadhubColors.Secondary.NeutralOne,
                modifier = Modifier.align(Alignment.CenterVertically)
            )

            else -> ButtonContent(
                model = model,
                style = style,
                isEnabled = isEnabled
            )
        }
    }
}

@Composable
private fun RowScope.ButtonContent(
    model: ButtonModel,
    style: ButtonStyle,
    isEnabled: Boolean
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Theme.spacing.x2)
    ) {
        model.leadingImage?.let {
            Image(
                image = it,
                modifier = Modifier.size(20.dp),
            )
        }
        Text(
            clause = model.title,
            style = style.toTextStyle(isEnabled = isEnabled)
        )
        model.trailingImage?.let {
            Image(
                image = it,
                modifier = Modifier.size(20.dp),
            )
        }
    }
}

@Composable
private fun RowScope.ButtonProgressBar(
    buttonColor: Color,
    modifier: Modifier,
) {
    CircularProgressIndicator(
        modifier = modifier.size(24.dp),
        color = buttonColor,
        strokeWidth = 2.dp,
        strokeCap = StrokeCap.Round
    )
}


@Composable
private fun ButtonColors.toButtonColors() = ButtonDefaults.buttonColors(
    backgroundColor = backgroundColor,
    disabledBackgroundColor = disabledBackgroundColor,
    contentColor = contentColor,
    disabledContentColor = disabledContentColor
)