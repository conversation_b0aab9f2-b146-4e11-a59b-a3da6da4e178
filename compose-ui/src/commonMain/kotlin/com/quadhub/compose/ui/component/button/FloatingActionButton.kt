package com.quadhub.compose.ui.component.button

import androidx.compose.foundation.layout.size
import androidx.compose.material.FloatingActionButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.theme.QuadhubColors


@Composable
fun FloatingActionButton(
    image: Image,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {

    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        backgroundColor = QuadhubColors.Destructive
    ) {
        Image(image = image, modifier = Modifier.size(22.dp))
    }

}