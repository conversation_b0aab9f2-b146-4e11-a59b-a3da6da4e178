package com.quadhub.compose.ui.component.button

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.quadhub.compose.ui.component.button.internals.ButtonColors
import com.quadhub.compose.ui.component.button.internals.ProvideButtonColors
import com.quadhub.compose.ui.component.button.model.ButtonModel
import com.quadhub.compose.ui.component.button.model.ButtonState
import com.quadhub.compose.ui.component.button.model.ButtonStyle
import com.quadhub.compose.ui.element.image.OptionalButtonCallback
import com.quadhub.compose.ui.theme.QuadhubColors
import com.quadhub.compose.ui.theme.Theme
import com.quadhub.compose.ui.theme.isSystemInDarkTheme

@Composable
fun PrimaryButton(
    model: ButtonModel,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    style: ButtonStyle = ButtonStyle.Regular,
    onClick: OptionalButtonCallback,
    invertedColors: Boolean = isSystemInDarkTheme(),
) {
    ProvideButtonColors(colors = componentColors(invertedColors = invertedColors)) {
        BaseButton(
            model = model,
            modifier = modifier,
            state = state,
            style = style,
            onClick = onClick,
        )
    }
}

@Composable
private fun componentColors(invertedColors: Boolean) = when (invertedColors) {
    true -> ButtonColors(
        backgroundColor = Theme.colors.elementInteractive.enabledOnDark,
        disabledBackgroundColor = Theme.colors.elementInteractive.disabledOnDark,
        textColor = Theme.colors.element.primaryOnDark,
        contentColor = Theme.colors.element.primaryOnDark,
        disabledTextColor = Theme.colors.elementInteractive.disabledOnDark
    )

    false -> ButtonColors(
        backgroundColor = Theme.colors.elementInteractive.enabledOnLight,
        disabledBackgroundColor = Theme.colors.elementInteractive.disabledOnLight,
        textColor = Theme.colors.element.primaryOnDark,
        disabledTextColor = QuadhubColors.Secondary.NeutralSix,
        contentColor = Theme.colors.element.primaryOnDark,
        disabledContentColor = QuadhubColors.Secondary.NeutralSix
    )
}