package com.quadhub.compose.ui.theme

import androidx.compose.ui.graphics.Color

object QuadhubColors {

    val White = Color(0xFFFFFFFF)
    val Black = Color(0xFF161817)

    val Destructive = Color(0xFFDC2626)

    val Input = Color(0xFFE2E8F0)

    object Primary {
        val Black = Color(0xFF000000)

        val Blue = Color(0xFF1D4ED8)
        val LightGray = Color(0xFFE5E5E5)

    }

    object Border {
        val Default = Color(0xFFD9D9D9)
    }

    object Secondary {
        val NeutralOne = Color(0xFF2C3A4B)
        val NeutralTwo = Color(0xFF394452)
        val NeutralFour = Color(0xFF6D7580)
        val NeutralFive = Color(0xFF858C94)
        val NeutralSix = Color(0xFFA5ABB3)

    }

    object Tertiary {

    }

    object Shadow {
        val lightSpot = Color(0x1F000000)
        val darkSpot = Color(0x1F000000)
    }
}
