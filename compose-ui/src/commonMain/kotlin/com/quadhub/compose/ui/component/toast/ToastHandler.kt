package com.quadhub.compose.ui.component.toast

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf


@Composable
fun WithToastHandler(
    interact: ToastInteract,
    content: @Composable () -> Unit
) {
    CompositionLocalProvider(
        LocalToastProvider provides interact,
        content = content
    )
}

val LocalToastProvider =
    compositionLocalOf<ToastInteract> { throw IllegalStateException("Toast host has not been setup") }