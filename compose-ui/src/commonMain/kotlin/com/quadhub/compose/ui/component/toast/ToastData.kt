package com.quadhub.compose.ui.component.toast

import androidx.compose.runtime.Immutable
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.text.Clause
import quadhubkmp.compose_ui.generated.resources.Res
import quadhubkmp.compose_ui.generated.resources.ic_caution
import quadhubkmp.compose_ui.generated.resources.ic_caution_reg
import quadhubkmp.compose_ui.generated.resources.ic_check


@Immutable
data class ToastData(
    val leadingIcon: Image,
    val title: Clause? = null,
    val description: Clause,
    val style: ToastStyle = ToastStyle.System,
    val isDismissible: Boolean = false
) {

    fun asDismissible(isDismissible: Boolean = true) = this.copy(isDismissible = isDismissible)

    companion object {


        val infoIcon = Image.Drawable(Res.drawable.ic_caution_reg)
        val cautionIcon = Image.Drawable(Res.drawable.ic_caution)
        val successIcon = Image.Drawable(Res.drawable.ic_check)

        fun withError(title: Clause? = null, description: Clause) = ToastData(
            leadingIcon = infoIcon,
            title = title,
            description = description,
            style = ToastStyle.Error
        )

        fun withInfo(title: Clause? = null, description: Clause) = ToastData(
            leadingIcon = infoIcon,
            title = title,
            description = description,
            style = ToastStyle.System
        )

        fun withSuccess(title: Clause? = null, description: Clause) = ToastData(
            leadingIcon = successIcon,
            title = title,
            description = description,
            style = ToastStyle.Success
        )
    }

}

@Immutable
enum class ToastStyle {
    Error, Success, System, Caution
}
