package com.quadhub.compose.ui.component.field

import quadhubkmp.compose_ui.generated.resources.Res
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.quadhub.compose.ui.component.field.model.TextInputModel
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.image.Icon
import quadhubkmp.compose_ui.generated.resources.sc_icon_eye_off
import quadhubkmp.compose_ui.generated.resources.sc_icon_eye_on


@Composable
fun PasswordLabelTextField(
    model: TextInputModel,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions(),
    modifier: Modifier = Modifier,
    onValueChange: (String) -> Unit,
    onPasswordToggleChange: ((Boolean) -> Unit)? = null
) {

    var isPasswordVisible by remember { mutableStateOf(false) }
    var hasFocus by remember { mutableStateOf(false) }

    LabelTextField(
        model = model,
        trailingContent = {
            PasswordIcon(
                isPasswordVisible = isPasswordVisible,
                onPasswordToggleChange = {
                    isPasswordVisible = !isPasswordVisible
                    onPasswordToggleChange?.invoke(isPasswordVisible)
                }
            )
        },
        visualTransformation = if (isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
        singleLine = true,
        keyboardActions = keyboardActions,
        keyboardOptions = keyboardOptions,
        onValueChange = onValueChange,
        modifier = modifier.onFocusChanged { hasFocus = it.hasFocus }
    )
}

@Composable
private fun PasswordIcon(
    isPasswordVisible: Boolean,
    onPasswordToggleChange: (Boolean) -> Unit
) {

    AnimatedContent(
        targetState = isPasswordVisible,
        transitionSpec = {
            fadeIn(
                animationSpec = tween(durationMillis = 500)
            ).togetherWith(fadeOut(animationSpec = tween(durationMillis = 500)))
        },
        label = "Password visibility image"
    ) { targetState ->
        Icon(
            image = if (targetState) Image.Drawable(Res.drawable.sc_icon_eye_on) else Image.Drawable(
                Res.drawable.sc_icon_eye_off
            ),
            modifier = Modifier.size(22.dp),
            onClick = {
                onPasswordToggleChange.invoke(isPasswordVisible)
            }
        )
    }
}