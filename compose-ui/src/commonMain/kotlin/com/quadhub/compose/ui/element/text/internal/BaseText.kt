package com.quadhub.compose.ui.element.text.internal

import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.text.Clause
import com.quadhub.compose.ui.element.text.ClauseStyle
import com.quadhub.compose.ui.element.text.CompositeClause
import com.quadhub.compose.ui.element.text.Graphics
import com.quadhub.compose.ui.element.text.toColor

@Composable
internal fun BaseText(
    clause: CompositeClause,
    style: TextStyle,
    maxLines: Int?,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    modifier: Modifier
) {

    val inlineContentMap =
        remember(clause.items.size) { GraphicsBuilder.buildGraphicsMap(graphics = clause.items, textStyle = style) }

    Text(
        text = ClauseParserBuilder.build(clause, style),
        maxLines = maxLines ?: Int.MAX_VALUE,
        lineHeight = if (lineHeight == TextUnit.Unspecified) style.lineHeight else lineHeight,
        modifier = modifier,
        style = style,
        overflow = overflow,
        inlineContent = inlineContentMap,
    )
}

private object GraphicsBuilder {
    fun buildGraphicsMap(
        graphics: List<Clause>,
        textStyle: TextStyle,
    ): Map<String, InlineTextContent> {
        val result = graphics.mapIndexedNotNull { index, clause ->
            when (clause) {
                is Graphics ->
                    Pair(
                        index.toString(),
                        InlineTextContent(
                            Placeholder(
                                clause.size?.sp ?: GRAPHICS_SIZE_DP.sp,
                                clause.size?.sp ?: GRAPHICS_SIZE_DP.sp,
                                PlaceholderVerticalAlign.TextCenter
                            )
                        ) {
                            Image(
                                image = clause.image,
                                modifier = Modifier.size(clause.size?.dp ?:GRAPHICS_SIZE_DP.dp),
                                colorFilter = when {
                                    clause.style is ClauseStyle -> ColorFilter.tint(clause.style.toColor())
                                    clause.blend -> ColorFilter.tint(textStyle.color)
                                    else -> null
                                }
                            )
                        }
                    )
                else -> null
            }
        }
        return result.associate { it.first to it.second }
    }

    private const val GRAPHICS_SIZE_DP = 20
}