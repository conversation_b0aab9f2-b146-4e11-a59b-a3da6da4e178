package com.quadhub.compose.ui.navigation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.quadhub.compose.ui.component.scaffold.Surface
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.navigation.model.BottomBarModel
import com.quadhub.compose.ui.navigation.model.BottomBarModelIcons
import com.quadhub.compose.ui.navigation.model.internal.BottomBarColors
import com.quadhub.compose.ui.navigation.model.internal.ProvideBottomBarColors
import com.quadhub.compose.ui.theme.QuadhubColors
import com.quadhub.compose.ui.theme.Theme
import com.quadhub.compose.ui.theme.isSystemInDarkTheme

@Composable
fun BottomBar(
    model: List<BottomBarModel>,
    isVisible: Boolean,
    modifier: Modifier = Modifier,
    onItemClick: (Int) -> Unit,
    invertedColors: Boolean = isSystemInDarkTheme(),
) {

    ProvideBottomBarColors(colors = componentColors(invertedColors = invertedColors)) {
        AnimatedVisibility(
            visible = isVisible,
            enter = slideInVertically(
                animationSpec = tween(
                    durationMillis = 250,
                    easing = LinearOutSlowInEasing
                ), initialOffsetY = { it }),
            exit = slideOutVertically(
                targetOffsetY = { it },
                animationSpec = tween(durationMillis = 250, easing = FastOutLinearInEasing)
            )
        ) {
            Surface {
                Row(
                    modifier = modifier
                        .height(60.dp)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    model.forEachIndexed { index, it ->
                        MenuItem(
                            isSelected = it.active,
                            modifier = Modifier
                                .weight(1f),
                            icon = it.icon,
                            onItemClick = { onItemClick.invoke(index) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MenuItem(
    icon: BottomBarModelIcons,
    isSelected: Boolean,
    modifier: Modifier,
    onItemClick: () -> Unit
) {

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.clickable(
            interactionSource = interactionSource,
            indication = null,
            enabled = true,
            onClick = onItemClick
        ),
    ) {
        Image(
            image = when (isSelected) {
                true -> icon.activeIcon
                else -> icon.inactiveIcon
            },
            modifier = Modifier
                .padding(vertical = 3.dp)
                .size(24.dp)
        )
    }
}

@Composable
private fun componentColors(invertedColors: Boolean): BottomBarColors {
    return when (invertedColors) {
        true -> BottomBarColors(
            activeIconColor = Theme.colors.element.primaryOnDark,
            inactiveIconColor = Theme.colors.elementInteractive.disabledOnDark,
            pressedColor = Theme.colors.elementInteractive.hoverTapOnDark,
        )

        false -> BottomBarColors(
            activeIconColor = QuadhubColors.Primary.Black,
            inactiveIconColor = QuadhubColors.Secondary.NeutralFour,
            pressedColor = QuadhubColors.Secondary.NeutralTwo,
        )
    }
}