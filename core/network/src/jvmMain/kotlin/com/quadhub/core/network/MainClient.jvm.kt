package com.quadhub.core.network

import io.ktor.client.HttpClient
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.websocket.WebSockets

actual fun getClient(): io.ktor.client.HttpClient {
    return HttpClient(OkHttp) {
        install(Logging) {
            logger = DebugLogger()
            level = LogLevel.ALL
        }

        install(WebSockets){
            pingIntervalMillis = 20_000
        }
    }
}