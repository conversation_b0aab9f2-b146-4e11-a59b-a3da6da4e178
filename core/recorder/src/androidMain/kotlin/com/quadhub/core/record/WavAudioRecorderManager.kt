package com.quadhub.core.record

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.core.content.ContextCompat
import com.quadhub.core.record.file.AudioRecordingFileProvider
import com.quadhub.core.record.model.RecordPermissionResult
import com.quadhub.core.record.model.RecordingCommandResult
import com.quadhub.core.record.model.RecordingState
import kotlinx.coroutines.*
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.io.*
import java.util.*
import kotlin.math.log10
import kotlin.math.sqrt

internal class WavAudioRecorderManager(
    private val context: Context,
    private val coroutineScope: CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
) : AudioRecorderManager {

    private var canRecord: Boolean? = null
    private lateinit var decibelJob: Job
    private val fileProvider = AudioRecordingFileProvider.create(context)
    private val _stateFlow = MutableStateFlow<RecordingState>(RecordingState.Idle)

    private lateinit var audioFile: File
    private var fileName = ""
    private var isRecording = false
    private lateinit var audioRecord: AudioRecord
    private lateinit var audioData: ByteArray
    private var totalAudioLen: Long = 0
    private var totalDataLen: Long = 0

    override fun observe(): Flow<RecordingState> = _stateFlow

    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    override fun startRecording(decibelRateInMs: Long, byteData: ((ByteArray) -> Unit)?): RecordingCommandResult {
        if (canRecord == null && !canRecord().isSuccess()) {
            return RecordingCommandResult.Failure
        }
        canRecord = true

        fileName = "${Date()}-recording.wav"
        audioFile = fileProvider.getFile(fileName)

        val bufferSize = AudioRecord.getMinBufferSize(
            SAMPLE_RATE_IN_HZ,
            CHANNEL_CONFIG,
            AUDIO_FORMAT
        )
        audioData = ByteArray(bufferSize)

        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            SAMPLE_RATE_IN_HZ,
            CHANNEL_CONFIG,
            AUDIO_FORMAT,
            bufferSize
        )

        return try {
            audioRecord.startRecording()
            isRecording = true
            decibelJob = startRecorderListenerJob(decibelRateInMs)

            coroutineScope.launch(Dispatchers.IO) {
                saveAudioToFile(byteDataListener = byteData)
            }

            RecordingCommandResult.Success
        } catch (e: Exception) {
            Log.e(LOG_TAG, "startRecording() failed", e)
            RecordingCommandResult.Failure
        }
    }

    override suspend fun stopRecording(): RecordingCommandResult {
        return try {
            isRecording = false
            audioRecord.stop()
            delay(100L)
            writeWavHeader()
            _stateFlow.emit(RecordingState.Completed(audioFilePath = fileName))
            RecordingCommandResult.Success
        } catch (ex: IllegalStateException) {
            ex.printStackTrace()
            RecordingCommandResult.Failure
        }
    }

    override fun pause(): RecordingCommandResult {
        return try {
            audioRecord.stop()
            decibelJob.cancel()
            RecordingCommandResult.Success
        } catch (ex: IllegalStateException) {
            ex.printStackTrace()
            RecordingCommandResult.Failure
        }
    }

    override fun resume(): RecordingCommandResult {
        return try {
            audioRecord.startRecording()
            decibelJob = startRecorderListenerJob(200)
            RecordingCommandResult.Success
        } catch (ex: IllegalStateException) {
            ex.printStackTrace()
            RecordingCommandResult.Failure
        }
    }

    override fun canRecord(): RecordPermissionResult {
        return when (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)) {
            PackageManager.PERMISSION_GRANTED -> RecordPermissionResult.Success
            else -> RecordPermissionResult.Denied
        }
    }

    override fun onCleared() {
        coroutineScope.cancel()
        if (_stateFlow.value != RecordingState.Idle) {
            audioRecord.release()
        }
    }

    private fun startRecorderListenerJob(delayInMs: Long = DELAY_IN_MS): Job {
        return coroutineScope.launch {
            while (isRecording) {
                delay(delayInMs)
                val decibelValue = calculateDecibelLevel()
                _stateFlow.emit(
                    RecordingState.Recording(
                        decibel = decibelValue,
                        seed = Random().nextInt(4532001)
                    )
                )
            }
        }
    }

    private fun saveAudioToFile(byteDataListener: ((ByteArray) -> Unit)?) {
        try {
            val tempFile = File(audioFile.absolutePath + ".temp")
            val outputStream = FileOutputStream(tempFile)

            while (isRecording) {
                val bytesRead = audioRecord.read(audioData, 0, audioData.size)
                if (bytesRead > 0) {
                    outputStream.write(audioData, 0, bytesRead)
                    totalAudioLen += bytesRead.toLong()

                    // justAdded
                    val latestByte = audioData.copyOf(bytesRead)
                    byteDataListener?.invoke(latestByte)
                }
            }
            outputStream.close()

            // Copy temp file to final WAV file
            tempFile.renameTo(audioFile)
        } catch (e: Exception) {
            Log.e(LOG_TAG, "saveAudioToFile() failed", e)
        }
    }

    private fun writeWavHeader() {
        try {
            totalDataLen = totalAudioLen + 36
            val fileInputStream = FileInputStream(audioFile)
            val fileOutputStream = FileOutputStream(File(audioFile.absolutePath + ".wav"))

            // WAV Header
            writeWavHeader(fileOutputStream)

            // Copy audio data
            val buffer = ByteArray(1024)
            var read: Int
            while (fileInputStream.read(buffer).also { read = it } != -1) {
                fileOutputStream.write(buffer, 0, read)
            }

            fileInputStream.close()
            fileOutputStream.close()

            // Replace original file with WAV file
            audioFile.delete()
            File(audioFile.absolutePath + ".wav").renameTo(audioFile)

        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    private fun writeWavHeader(out: FileOutputStream) {
        val channels = 1
        val byteRate = SAMPLE_RATE_IN_HZ * channels * BITS_PER_SAMPLE / 8

        val header = ByteArray(44)
        header[0] = 'R'.code.toByte()  // RIFF/WAVE header
        header[1] = 'I'.code.toByte()
        header[2] = 'F'.code.toByte()
        header[3] = 'F'.code.toByte()
        header[4] = (totalDataLen and 0xffL).toByte()  // file length
        header[5] = (totalDataLen shr 8 and 0xffL).toByte()
        header[6] = (totalDataLen shr 16 and 0xffL).toByte()
        header[7] = (totalDataLen shr 24 and 0xffL).toByte()
        header[8] = 'W'.code.toByte()  // WAVE
        header[9] = 'A'.code.toByte()
        header[10] = 'V'.code.toByte()
        header[11] = 'E'.code.toByte()
        header[12] = 'f'.code.toByte()  // 'fmt ' chunk
        header[13] = 'm'.code.toByte()
        header[14] = 't'.code.toByte()
        header[15] = ' '.code.toByte()
        header[16] = 16  // 4 bytes: size of 'fmt ' chunk
        header[17] = 0
        header[18] = 0
        header[19] = 0
        header[20] = 1  // format = 1 (PCM)
        header[21] = 0
        header[22] = channels.toByte()  // mono
        header[23] = 0
        header[24] = (SAMPLE_RATE_IN_HZ and 0xff).toByte()  // sampling rate
        header[25] = (SAMPLE_RATE_IN_HZ shr 8 and 0xff).toByte()
        header[26] = (SAMPLE_RATE_IN_HZ shr 16 and 0xff).toByte()
        header[27] = (SAMPLE_RATE_IN_HZ shr 24 and 0xff).toByte()
        header[28] = (byteRate and 0xff).toByte()  // byte rate
        header[29] = (byteRate shr 8 and 0xff).toByte()
        header[30] = (byteRate shr 16 and 0xff).toByte()
        header[31] = (byteRate shr 24 and 0xff).toByte()
        header[32] = (channels * BITS_PER_SAMPLE / 8).toByte()  // block align
        header[33] = 0
        header[34] = BITS_PER_SAMPLE.toByte()  // bits per sample
        header[35] = 0
        header[36] = 'd'.code.toByte()  // data chunk
        header[37] = 'a'.code.toByte()
        header[38] = 't'.code.toByte()
        header[39] = 'a'.code.toByte()
        header[40] = (totalAudioLen and 0xffL).toByte()
        header[41] = (totalAudioLen shr 8 and 0xffL).toByte()
        header[42] = (totalAudioLen shr 16 and 0xffL).toByte()
        header[43] = (totalAudioLen shr 24 and 0xffL).toByte()

        out.write(header, 0, 44)
    }

    private fun calculateDecibelLevel(): Int {
        if (audioData.isEmpty()) return 0

        // Convert byte array to short array
        val shorts = ShortArray(audioData.size / 2)
        for (i in shorts.indices) {
            shorts[i] = (audioData[i * 2].toInt() and 0xFF or
                    (audioData[i * 2 + 1].toInt() shl 8)).toShort()
        }

        // Calculate RMS (Root Mean Square)
        var sumOfSquares = 0.0
        for (sample in shorts) {
            // Convert to range [-1, 1]
            val normalizedSample = sample / 32768.0
            sumOfSquares += normalizedSample * normalizedSample
        }
        val rms = sqrt(sumOfSquares / shorts.size)

        // Convert RMS to percentage
        // Using a log scale to better represent human perception of loudness
        // Adding small value to avoid log(0)
        val db = 20 * log10(rms + 0.0000001)

        // Convert from dB to percentage
        // Typical values: -60dB (very quiet) to 0dB (maximum)
        // Map -60dB -> 0% and 0dB -> 100%
        val percentage = ((db + 60) / 60 * 100)
            .coerceIn(0.0, 100.0)
            .toInt()

        return percentage
    }

    companion object {
        private const val LOG_TAG = "WAV_AUDIO_RECORDER"
        private const val DELAY_IN_MS = 100L
        private const val SAMPLE_RATE_IN_HZ = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BITS_PER_SAMPLE = 16
    }
}