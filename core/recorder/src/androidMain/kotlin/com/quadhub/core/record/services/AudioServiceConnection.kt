package com.quadhub.core.record.services

import android.content.ComponentName
import android.content.ServiceConnection
import android.os.IBinder

class AudioServiceConnection constructor() : ServiceConnection {

    private lateinit var mService: AudioRecorderService
    private var mBound: Boolean = false

    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
        // We've bound to LocalService, cast the IBinder and get LocalService instance.
        val binder = service as AudioRecorderService.LocalBinder
        mService = binder.getService()
        mBound = true
    }

    override fun onServiceDisconnected(name: ComponentName?) {
        mBound = false
    }
}