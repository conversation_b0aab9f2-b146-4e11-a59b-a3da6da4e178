package com.quadhub.core.record.file

import android.content.Context
import android.net.Uri
import android.os.Environment
import androidx.core.content.FileProvider
import java.io.File

interface AudioRecordingFileProvider {
    fun getUri(filePath: String): Uri
    fun getFile(fileName: String): File

    companion object {
        fun create(context: Context): AudioRecordingFileProvider =
            AudioRecordingFileProviderImpl(context = context)
    }
}

internal class AudioRecordingFileProviderImpl constructor(
    private val context: Context
) : AudioRecordingFileProvider {

    override fun getUri(filePath: String): Uri {
        if (filePath.startsWith("http")) return Uri.parse(filePath)
        val file = File(context.getExternalFilesDir(Environment.DIRECTORY_MUSIC), filePath)
        val fileUri = FileProvider.getUriForFile(context, "com.quadhub.app.provider", file)
        return fileUri
    }

    override fun getFile(fileName: String): File {
        return File(context.getExternalFilesDir(Environment.DIRECTORY_MUSIC), fileName)
    }

    private fun File.getFilePath(): String {
        val uri = FileProvider.getUriForFile(context, "com.quadhub.app.provider", this)
        return requireNotNull(uri.path)
    }

}