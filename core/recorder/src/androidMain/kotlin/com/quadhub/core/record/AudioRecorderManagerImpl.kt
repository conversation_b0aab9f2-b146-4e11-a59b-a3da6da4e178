package com.quadhub.core.record

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.MediaRecorder
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import com.quadhub.core.record.file.AudioRecordingFileProvider
import com.quadhub.core.record.model.RecordPermissionResult
import com.quadhub.core.record.model.RecordingCommandResult
import com.quadhub.core.record.model.RecordingState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.io.IOException
import java.util.Date
import java.util.Random


internal class AudioRecorderManagerImpl(
    private val context: Context,
    private val coroutineScope: CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
) : AudioRecorderManager  {

    private var canRecord: Boolean? = null
    private lateinit var decibelJob: Job

    private val fileProvider : AudioRecordingFileProvider = AudioRecordingFileProvider.create(context)

    private val _stateFlow: MutableStateFlow<RecordingState> =
        MutableStateFlow(value = RecordingState.Idle)

    private lateinit var audioFile: File
    private var fileName = "";

    private var isRecording = false

    private lateinit var recorder: MediaRecorder

    override fun observe(): Flow<RecordingState> = _stateFlow

    override fun startRecording(decibelRateInMs: Long, byteData: ((ByteArray) -> Unit)?): RecordingCommandResult {
        if (canRecord == null && !this.canRecord().isSuccess()) {
            return RecordingCommandResult.Failure
        }
        canRecord = true

        // Record to the external cache directory for visibility
        this.fileName = "${Date()}-recording.mp4"
        this.audioFile = fileProvider.getFile(this.fileName)

        recorder =
            (if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) MediaRecorder(context) else MediaRecorder()).apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                setAudioEncoder(MediaRecorder.AudioEncoder.AMR_WB)
                setOutputFile(audioFile.absolutePath)
            }

        return try {
            this.recorder.prepare()
            this.recorder.start()

            this.isRecording = true
            this.decibelJob = this.startRecorderListenerJob(decibelRateInMs)
            RecordingCommandResult.Success
        } catch (e: IOException) {
            Log.e(LOG_TAG, "prepare() failed")
            RecordingCommandResult.Failure
        }
    }

    override suspend fun stopRecording(): RecordingCommandResult {
        return try {
            this.isRecording = false
            this.recorder.stop()
            delay(100L)
            _stateFlow.emit(RecordingState.Completed(audioFilePath = this.fileName))
            RecordingCommandResult.Success
        } catch (ex: IllegalStateException) {
            ex.printStackTrace()
            RecordingCommandResult.Failure
        }
    }

    override fun pause(): RecordingCommandResult {
        return try {
            this.recorder.pause()
            this.decibelJob.cancel()
            RecordingCommandResult.Success
        } catch (ex: IllegalStateException) {
            ex.printStackTrace()
            RecordingCommandResult.Failure
        }
    }

    override fun resume(): RecordingCommandResult {
        return try {
            this.recorder.resume()
            this.decibelJob = this.startRecorderListenerJob(200)
            RecordingCommandResult.Success
        } catch (ex: IllegalStateException) {
            ex.printStackTrace()
            RecordingCommandResult.Failure
        }
    }

    override fun canRecord(): RecordPermissionResult {
        return when (ContextCompat.checkSelfPermission(
            this.context,
            Manifest.permission.RECORD_AUDIO
        )) {
            PackageManager.PERMISSION_GRANTED -> RecordPermissionResult.Success
            else -> RecordPermissionResult.Denied
        }
    }

    override fun onCleared() {
        this.coroutineScope.cancel()
        val currentState = this._stateFlow.value
        Log.e("AudioRecorder", "Current state is $currentState")
        if (currentState != RecordingState.Idle) {
            this.recorder.release()
        }
    }

    /**
     * start Job
     * val job = startRepeatingJob()
     * cancels the job and waits for its completion
     * job.cancelAndJoin()
     * Params
     * timeInterval: time milliSeconds
     */
    private fun startRecorderListenerJob(delayInMs: Long = DELAY_IN_MILL): Job {
        return coroutineScope.launch {
            while (isRecording) {
                // add your task here
                delay(delayInMs)
                val decibelValue = recorder.maxAmplitude.toDouble()
                val decibelIntValue = processDecibel(decibelValue)
                _stateFlow.emit(RecordingState.Recording(decibel = decibelIntValue, seed = Random().nextInt(4532001)))
            }
        }
    }

    private fun processDecibel(value: Double?): Int {
        val abValue = value ?: 0.0
        val tValue = if (abValue > P_MAX_DECIBEL_VALUE) P_MAX_DECIBEL_VALUE else abValue
        return ((tValue / P_MAX_DECIBEL_VALUE) * 100).toInt()
    }

    companion object {
        private const val LOG_TAG = "AUDIO_RECORDER"

        const val DELAY_IN_MILL = 100L
        const val P_MAX_DECIBEL_VALUE = 20000.0
    }
}