package com.quadhub.core.record

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Build
import android.os.IBinder
import com.quadhub.core.record.services.AudioRecorderService
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

actual fun buildPersistentAudioRecorder(context: Any): PersistentAudioRecorderManager {
    return PersistentAudioRecorderManagerImpl(context as Context)
}

internal class PersistentAudioRecorderManagerImpl constructor(
    private val appContext: Context
) : PersistentAudioRecorderManager {

    private var mBound: Boolean = false
    private var mStarted: Boolean = false

    private lateinit var connection: ServiceConnection
    private lateinit var audioManager: AudioRecorderManager

    private val intent = Intent(appContext, AudioRecorderService::class.java)

    override suspend fun create(): AudioRecorderManager {
        return suspendCoroutine { continuation ->
            connection = object : ServiceConnection {
                override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                    val binder = service as AudioRecorderService.LocalBinder
                    mBound = true
                    val manager = binder.getService().recorderManager
                    audioManager = manager
                    continuation.resume(manager)
                }
                override fun onServiceDisconnected(name: ComponentName?) {
                    mBound = false
                }
            }
            appContext.bindService(intent, connection, Context.BIND_AUTO_CREATE)
        }
    }

    override fun start() {
        mStarted = true
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            appContext.startForegroundService(intent)
        }
    }

    override fun unbind() {
        if (mBound) {
            appContext.unbindService(connection)
            appContext.stopService(intent)
        }
    }

}