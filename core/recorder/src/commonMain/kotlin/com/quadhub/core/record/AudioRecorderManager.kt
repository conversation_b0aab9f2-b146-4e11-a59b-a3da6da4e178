package com.quadhub.core.record


import com.quadhub.core.record.model.RecordPermissionResult
import com.quadhub.core.record.model.RecordingCommandResult
import com.quadhub.core.record.model.RecordingState
import kotlinx.coroutines.flow.Flow

interface AudioRecorderManager {
    fun observe(): Flow<RecordingState>
    fun startRecording(decibelRateInMs: Long, byteData: ((ByteArray) -> Unit)? = null): RecordingCommandResult
    suspend fun stopRecording(): RecordingCommandResult
    fun pause(): RecordingCommandResult
    fun resume(): RecordingCommandResult
    fun canRecord(): RecordPermissionResult
    fun onCleared()
}

/**
 * Core app should build this audio recorder.
 * This module can be open for feature modules to consume the usecase.
 */
expect fun buildAudioRecorder(context: Any): AudioRecorderManager