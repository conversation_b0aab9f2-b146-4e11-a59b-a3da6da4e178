package com.quadhub.core.permissions


import com.quadhub.core.permissions.model.PermissionsRequest
import com.quadhub.core.permissions.model.PermissionsResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.suspendCancellableCoroutine
import platform.AVFAudio.AVAudioSession
import kotlin.coroutines.resume

internal class PermissionsManagerImpl constructor(
    private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Unconfined + SupervisorJob())
) : PermissionsManager {

    override fun requestPermission(request: PermissionsRequest): Flow<PermissionsResult> {
        return flow {
            when (request) {
                is PermissionsRequest.Audio -> emit(requestAudioPermission())
            }
        }.shareIn(scope = coroutineScope, started = SharingStarted.WhileSubscribed())
    }

    private suspend fun requestAudioPermission(): PermissionsResult {
        return suspendCancellableCoroutine { continuation ->
            val audioSession = AVAudioSession.sharedInstance()
            audioSession.requestRecordPermission { allowed ->
                when (allowed) {
                    true -> continuation.resume(PermissionsResult.Success)
                    false -> continuation.resume(PermissionsResult.Denied)
                }
            }
        }
    }
}