package com.quadhub.core.permissions.mapper

import android.Manifest
import android.os.Build
import com.quadhub.core.permissions.model.PermissionsRequest

internal interface PermissionRequestMapper {
    fun map(request: PermissionsRequest): Array<String>
}

internal class PermissionRequestMapperImpl constructor() : PermissionRequestMapper {

    override fun map(request: PermissionsRequest): Array<String> {
        return when (request) {
            is PermissionsRequest.Audio -> when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> arrayOf(
                    Manifest.permission.RECORD_AUDIO,
                    Manifest.permission.POST_NOTIFICATIONS
                )
                else -> arrayOf(Manifest.permission.RECORD_AUDIO)
            }
        }
    }

}