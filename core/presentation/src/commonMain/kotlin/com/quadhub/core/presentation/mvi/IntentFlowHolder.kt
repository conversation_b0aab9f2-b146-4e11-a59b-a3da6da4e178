package com.quadhub.core.presentation.mvi

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.toList


interface IntentFlowHolder<Intent> {
    val intents: Flow<Intent>
    suspend fun emit(intent: Intent)
}

class SharedIntentFlowHolder<Intent> : IntentFlowHolder<Intent> {

    private val _intents: MutableSharedFlow<Intent> = MutableSharedFlow()

    override val intents: Flow<Intent>
        get() = _intents.asSharedFlow()

    override suspend fun emit(intent: Intent) {
        _intents.emit(intent)
    }
}

/**
 * This should only be used in unit tests
 * @return the single item emitted by the flow
 * @throws IllegalStateException if the flow emits more than one item
 */
suspend fun <T> Flow<T>.toSingle(): T {
    val results = toList()
    require (results.size == 1) { "Flow should emit only one item" }
    return toList().first()
}
