package com.quadhub.core.presentation.navigation.compose

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.navigation.NavBackStackEntry


enum class PageTransition(internal val spec: PageTransitionSpec) {

    TopLevel(
        object : PageTransitionSpec {
            override fun enterTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): EnterTransition {
                return EnterTransition.None
            }

            override fun exitTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): ExitTransition {
                return ExitTransition.None
            }

            override fun popEnterTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): EnterTransition {
                return EnterTransition.None
            }

            override fun popExitTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): ExitTransition {
                return ExitTransition.None
            }
        }
    ),

    Hierarchical(
        object : PageTransitionSpec {
            private val transitionDuration = 200
            private val offsetDivider = 8
            private val emphasisedEasing = FastOutSlowInEasing
//                Easing {
//                val path = Path().apply {
//                    moveTo(0f, 0f)
//                    cubicTo(0.05f, 0f, 0.13333f, 0.06f, 0.16666f, 0.4f)
//                    cubicTo(0.208333f, 0.82f, 0.25f, 1f, 1f, 1f)
//                }
//                PathInterpolator(path).getInterpolation(it)
//            }

            override fun enterTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): EnterTransition {
                return scope.slideIntoContainer(
                    towards = AnimatedContentTransitionScope.SlideDirection.Start,
                    animationSpec = tween(
                        durationMillis = transitionDuration,
                        easing = emphasisedEasing
                    ),
                    initialOffset = { it / offsetDivider }
                ) + fadeIn(tween(durationMillis = transitionDuration, easing = emphasisedEasing))
            }

            override fun exitTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): ExitTransition {
                return scope.slideOutOfContainer(
                    towards = AnimatedContentTransitionScope.SlideDirection.Start,
                    animationSpec = tween(
                        durationMillis = transitionDuration,
                        easing = emphasisedEasing
                    ),
                    targetOffset = { it / offsetDivider }
                ) + fadeOut(tween(durationMillis = transitionDuration, easing = emphasisedEasing))
            }

            override fun popEnterTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): EnterTransition {
                return scope.slideIntoContainer(
                    towards = AnimatedContentTransitionScope.SlideDirection.End,
                    animationSpec = tween(
                        durationMillis = transitionDuration,
                        easing = emphasisedEasing
                    ),
                    initialOffset = { it / offsetDivider }
                )  + fadeIn(tween(durationMillis = transitionDuration, easing = emphasisedEasing))
            }

            override fun popExitTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): ExitTransition {
                return scope.slideOutOfContainer(
                    towards = AnimatedContentTransitionScope.SlideDirection.End,
                    animationSpec = tween(
                        durationMillis = transitionDuration,
                        easing = emphasisedEasing
                    ),
                    targetOffset = { it / offsetDivider }
                ) + fadeOut(tween(durationMillis = transitionDuration, easing = emphasisedEasing))
            }
        }
    )

}

internal interface PageTransitionSpec {
    fun enterTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): EnterTransition
    fun exitTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): ExitTransition
    fun popEnterTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): EnterTransition
    fun popExitTransition(scope: AnimatedContentTransitionScope<NavBackStackEntry>): ExitTransition
}