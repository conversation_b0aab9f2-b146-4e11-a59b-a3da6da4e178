package com.quadhub.core.presentation.navigation.compose

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.platform.UriHandler
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavHostController
import androidx.navigation.Navigator
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.quadhub.core.presentation.navigation.ComposeNavigationDispatcher

@Composable
fun NavContainer(
    modifier: Modifier,
    startDestination: String,
    navigatorHost: NavigatorHost.Root,
    navHostController: NavHostController,
    uriHandler: UriHandler = LocalUriHandler.current,
    pageTransition: PageTransition = PageTransition.Hierarchical,
) {
    val navDispatcher = remember {
        ComposeNavigationDispatcher(
            screenNavigationController = navHostController,
            uriHandler = uriHandler,
            addArgs = ComposeNavigationDispatcher.Companion::addArguments
        )
    }

    NavHost(
        navController = navHostController,
        startDestination = startDestination,
        enterTransition = pageTransition.spec::enterTransition,
        exitTransition = pageTransition.spec::exitTransition,
        popEnterTransition = pageTransition.spec::popEnterTransition,
        popExitTransition = pageTransition.spec::popExitTransition,
        modifier = modifier
    ) {
        navigatorHost.buildGraph(
            builder = this,
            navigationDispatcher = navDispatcher
        )
    }
}

@Composable
fun rememberNavController(
    navigators: Array<Navigator<out NavDestination>> = emptyArray(),
    onDestinationChangedListener: NavController.OnDestinationChangedListener
): NavHostController {
    var destinationChangedListenerAttached by remember { mutableStateOf(false) }
    val navController = rememberNavController(navigators = navigators)
    if (!destinationChangedListenerAttached) {
        destinationChangedListenerAttached = true
        navController.addOnDestinationChangedListener(listener = onDestinationChangedListener)
    }

    return navController
}
