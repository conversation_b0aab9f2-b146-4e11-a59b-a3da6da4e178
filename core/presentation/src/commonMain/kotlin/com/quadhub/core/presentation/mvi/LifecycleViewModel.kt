package com.quadhub.core.presentation.mvi

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel


abstract class LifecycleViewModel<Intent, Result, ViewState> internal constructor(
    private val initialState: ViewState,
    private val mviProcessor: MviProcessor<Intent, Result, ViewState>,
) : ViewModel() {

    constructor(
        args: ViewModelArgs<Intent, Result, ViewState>
    ) : this(
        initialState = args.initialState,
        mviProcessor = args.buildMviProcessor()
    )

    private val mutableState = mutableStateOf(initialState)
    private val firstIntent = mutableStateOf(true)
    val isFirstIntent: Boolean get() = firstIntent.value
    val state: State<ViewState> get() = mutableState

    init {
        mviProcessor.bind(observer = { mutableState.value = it })
    }

    fun emit(intent: Intent) {
        print("Emit intent: $intent")
        firstIntent.value = false
        mviProcessor.emit(intent)
    }

    final override fun onCleared() {
        mviProcessor.dispose()
        print("Going to be cleared")
        super.onCleared()
    }

    abstract class ViewModelArgs<Intent, Result, ViewState> {
        internal abstract val initialState: ViewState
        internal abstract fun buildMviProcessor(): MviProcessor<Intent, Result, ViewState>
    }
}