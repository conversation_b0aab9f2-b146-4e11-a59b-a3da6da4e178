package com.quadhub.core.presentation.navigation

import androidx.compose.ui.platform.UriHandler
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController


class ComposeNavigationDispatcher(
    private val screenNavigationController: <PERSON>v<PERSON><PERSON><PERSON>er,
    private val uriHandler: <PERSON>riH<PERSON><PERSON>,
    private val addArgs: NavBackStackEntry.(Map<String, NavigationArgument>) -> Unit,
) : NavigationDispatcher {
    override fun navigateTo(command: NavigationCommand) {
        when (command) {
            is NavigationCommand.BackCommand -> {
                screenNavigationController.previousBackStackEntry?.addArgs(command.result)
                screenNavigationController.popBackStack()
            }
            is NavigationCommand.ScreenCommand -> {
                when (command.clearStack) {
                    true -> {
                        val result = screenNavigationController.popBackStack(
                            route = command.route,
                            inclusive = false,
                            saveState = false
                        )
                        if (!result) {
                            screenNavigationController.navigate(command.route)
                        }
                    }

                    false -> screenNavigationController.navigate(command.route)
                }
                screenNavigationController.currentBackStackEntry?.addArgs(command.arguments)
            }
            is NavigationCommand.CloseFeature -> {
                val currentRoute = screenNavigationController.currentDestination?.parent?.route
                val result = screenNavigationController.popBackStack(
                    route = currentRoute!!,
                    inclusive = false,
                    saveState = false
                )
//                if (!result) {
//                    screenNavigationController.navigate(command.route)
//                }
                screenNavigationController.currentBackStackEntry?.addArgs(command.result)
            }
            is NavigationCommand.FeatureCommand -> {
                when(command.clearStack) {
                    true -> screenNavigationController.navigate(
                        route = command.featureName,
                        builder = {
                            var popResult = screenNavigationController.popBackStack()
                            while (popResult) {
                                popResult = screenNavigationController.popBackStack()
                            }
                        }
                    )
                    false -> screenNavigationController.navigate(route = command.featureName)
                }
                screenNavigationController.currentBackStackEntry?.let { addArguments(it, command.arguments) }
            }
            is NavigationCommand.ExternalUriCommand -> uriHandler.openUri(command.uri)
        }
    }

    companion object {

        fun addArguments(backStackEntry: NavBackStackEntry, args: Map<String, NavigationArgument>) {
            with(backStackEntry.savedStateHandle) {
                args.forEach { (key, args) ->
                    when (args) {
                        is NavigationArgument.IntArgument -> set(key, args.value)
                        is NavigationArgument.StringArgument -> set(key, args.value)
                        is NavigationArgument.LongArgument -> set(key, args.value)
                        is NavigationArgument.BooleanArgument -> set(key, args.value)
                        is NavigationArgument.FloatArgument -> set(key, args.value)
                        is NavigationArgument.ParcelableArgument -> set(key, args.value)
                    }
                }
            }
        }

    }
}
