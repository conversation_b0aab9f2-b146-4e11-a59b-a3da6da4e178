package com.quadhub.core.player


sealed class PlaybackState {
    data object Idle : PlaybackState()
    data class Paused(val currentPosition: Int) : PlaybackState()
    data object Stopped : PlaybackState()
    data class Playing(val currentPosition: Int, val duration: Int) : PlaybackState()
}

sealed class CommandResult {
    data object Success : CommandResult()
    data object Failure : CommandResult()
}