package com.quadhub.core.player

import kotlinx.cinterop.ExperimentalForeignApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import platform.AVFAudio.AVAudioPlayer
import platform.AVFAudio.AVAudioSession
import platform.AVFAudio.AVAudioSessionCategoryOptionDefaultToSpeaker
import platform.AVFAudio.AVAudioSessionCategoryPlayback
import platform.AVFAudio.AVAudioSessionModeMoviePlayback
import platform.AVFAudio.setActive
import platform.Foundation.NSURL

@OptIn(ExperimentalForeignApi::class)
internal class AudioPlayerManagerImpl constructor(
    private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.IO)
) : AudioPlayerManager {

    private val audioSession: AVAudioSession = AVAudioSession.sharedInstance()
    private val _playbackFlow = MutableStateFlow<PlaybackState>(PlaybackState.Idle)
    private var player: AVAudioPlayer? = null
    private var playbackJob: Job? = null

    init {
        this.audioSession.setCategory(
            category = AVAudioSessionCategoryPlayback,
            mode = AVAudioSessionModeMoviePlayback,
            options = AVAudioSessionCategoryOptionDefaultToSpeaker,
            error = null
        )
        this.audioSession.setActive(active = true, error = null)
    }

    override fun isPlaying(): Boolean {
        return player?.isPlaying() ?: false
    }

    override suspend fun play(path: String): CommandResult {
        return try {
            this.player = AVAudioPlayer(contentsOfURL = NSURL(fileURLWithPath = path), error = null)
            this.player?.play()
            this.playbackJob = this.startPlaybackJobListenerJob()
            CommandResult.Success
        } catch (exception: Exception) {
            exception.printStackTrace()
            CommandResult.Failure
        }
    }

    override suspend fun play(): CommandResult {
        val player = this.player ?: return CommandResult.Failure
        player.play()
        return CommandResult.Failure
    }

    override fun observePlaybackState(): Flow<PlaybackState> {
        return _playbackFlow
    }

    override suspend fun pause(): CommandResult {
        val player = this.player ?: return CommandResult.Failure
        player.pause()
        return CommandResult.Success
    }

    override suspend fun stop(): CommandResult {
        val player = this.player ?: return CommandResult.Failure
        player.stop()
        this.player = null
        _playbackFlow.emit(PlaybackState.Stopped)
        return CommandResult.Success
    }

    override suspend fun seekTo(position: Int): CommandResult {
        val player = this.player ?: return CommandResult.Failure
        val timeInSeconds = position.toDouble() / 1000
        player.setCurrentTime(timeInSeconds)
        return CommandResult.Success
    }

    override suspend fun moveBy(timeInMills: Int): CommandResult {
        val player = this.player ?: return CommandResult.Failure
        val timeInSeconds = timeInMills.toDouble() / 1000
        player.setCurrentTime(timeInSeconds)
        return CommandResult.Success
    }

    private fun startPlaybackJobListenerJob(delayInMs: Long = DELAY_IN_MILL): Job? {
        val player = this.player ?: return null
        return coroutineScope.launch {
            while (true) {
                // add your task here
                delay(delayInMs)
                val currentPosition = player.currentTime.toMills()
                val duration = player.duration.toMills()
                _playbackFlow.emit(PlaybackState.Playing(currentPosition = currentPosition, duration = duration))
            }
        }
    }

    /**
     * Return the Mills
     */
    private fun Double.toMills() = toInt() * 1000

    companion object {
        private const val LOG_TAG = "Quadhub:Player"
        private const val DELAY_IN_MILL = 200L
    }
}