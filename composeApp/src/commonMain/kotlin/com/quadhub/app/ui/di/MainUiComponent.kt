package com.quadhub.app.ui.di

import com.quadhub.app.ui.MainContract.Intent
import com.quadhub.app.ui.MainContract.Result
import com.quadhub.app.ui.MainContract.ViewState
import com.quadhub.app.ui.MainInteractor
import com.quadhub.app.ui.MainMapper
import com.quadhub.app.ui.MainReducer
import com.quadhub.app.ui.MainViewModel
import com.quadhub.core.presentation.mvi.Interactor
import com.quadhub.core.presentation.mvi.Reducer
import me.tatarka.inject.annotations.Component
import me.tatarka.inject.annotations.Provides

@Component
internal abstract class MainUiComponent {
    abstract val viewModel: MainViewModel

    @Provides
    protected fun provideDefaultState(): ViewState = ViewState.Default

    @Provides
    protected fun provideInteractor(): Interactor<Intent, Result> =
        MainInteractor(mapper = MainMapper())

    @Provides
    fun provideReducer(): Reducer<ViewState, Result> = MainReducer()
}