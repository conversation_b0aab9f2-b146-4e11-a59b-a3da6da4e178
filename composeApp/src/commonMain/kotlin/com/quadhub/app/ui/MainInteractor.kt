package com.quadhub.app.ui

import com.quadhub.app.ui.MainContract.Intent
import com.quadhub.app.ui.MainContract.Mapper
import com.quadhub.app.ui.MainContract.Result
import com.quadhub.core.presentation.mvi.Interactor
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge

@OptIn(ExperimentalCoroutinesApi::class)
internal class MainInteractor constructor(
    private val mapper: Mapper,
) : Interactor<Intent, Result> {

    override fun process(intents: Flow<Intent>): Flow<Result> {
        return merge(
            intents.filterIsInstance<Intent.Initial>().map(mapper::toResult),
            intents.filterIsInstance<Intent.OnBottomNavBarClick>().map(mapper::toResult),
            intents.filterIsInstance<Intent.RouteChanged>().map(mapper::toResult)
        )
    }

}
