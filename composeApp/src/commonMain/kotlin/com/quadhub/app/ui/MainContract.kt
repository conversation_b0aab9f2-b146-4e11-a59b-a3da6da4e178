package com.quadhub.app.ui

import androidx.compose.runtime.Immutable
import com.quadhub.app.ui.model.BottomBarState
import com.quadhub.app.ui.model.MainBottomBarModel
import com.quadhub.core.presentation.navigation.NavigationCommand
import com.quadhub.core.presentation.navigation.compose.PageTransition

internal interface MainContract {

    interface Mapper {
        fun toResult(intent: Intent.Initial): Result.Initial
        fun toResult(intent: Intent.RouteChanged): Result.RouteChanged

        fun toResult(intent: Intent.OnBottomNavBarClick) : Result.BottomNavigationUpdated
    }

    sealed class Intent {
        data object Initial : Intent()
        data class RouteChanged(val previousRoute: String, val route: String) : Intent()
        data object ClearNav : Intent()
        data class OnBottomNavBarClick(val model: MainBottomBarModel) : Intent()
    }

    sealed class Result {

        data class Initial(
            val startDestinationRoute: String
        ) : Result()
        data object ClearNav : Result()
        data object AlertDismissed: Result()

        data class BottomNavigationUpdated(
            val bottomModel: List<MainBottomBarModel>,
            val navigationCommand: NavigationCommand
        ) : Result()

        data class NavigationCommandUpdated(
            val navCommand: NavigationCommand
        ): Result()

        data class RouteChanged(
            val route: String,
            val pageTransition: PageTransition,
            val bottomBarState: BottomBarState,
        ) : Result()
    }

    @Immutable
    data class ViewState(
        val navCommand: NavigationCommand? = null,
        val startDestinationRoute: String? = null,
        val bottomModel: List<MainBottomBarModel>?,
        val bottomState: BottomBarState,
        val currentRoute: String?,
        val pageTransition: PageTransition
    ) {
        companion object {
            val Default = ViewState(
                navCommand = null,
                startDestinationRoute = null,
                currentRoute = null,
                bottomModel = MainBottomBarModel.Default,
                bottomState = BottomBarState.Default,
                pageTransition = PageTransition.Hierarchical,
            )
        }
    }


}