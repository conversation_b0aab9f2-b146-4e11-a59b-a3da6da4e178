package com.quadhub.app.ui

import me.tatarka.inject.annotations.Inject
import com.quadhub.app.ui.MainContract.Mapper
import com.quadhub.app.ui.MainContract.Intent
import com.quadhub.app.ui.MainContract.Result
import com.quadhub.app.ui.model.BottomBarState
import com.quadhub.app.ui.model.BottomNavigationType
import com.quadhub.app.ui.model.MainBottomBarModel
import com.quadhub.core.presentation.navigation.NavigationCommand
import com.quadhub.core.presentation.navigation.compose.PageTransition

internal class MainMapper @Inject constructor() : Mapper {

    override fun toResult(intent: Intent.Initial): Result.Initial {
        return Result.Initial("auth")
    }

    override fun toResult(intent: Intent.RouteChanged) = Result.RouteChanged(
        route = intent.route,
        pageTransition = when (isTopLevelScreen(intent.route) && isTopLevelScreen(intent.previousRoute)) {
            true -> PageTransition.TopLevel
            false -> PageTransition.Hierarchical
        },
        bottomBarState = BottomBarState(
            show = isTopLevelScreen(intent.route)
        )
    )

    override fun toResult(intent: Intent.OnBottomNavBarClick): Result.BottomNavigationUpdated {
        return Result.BottomNavigationUpdated(
            bottomModel = MainBottomBarModel.select(intent.model),
            navigationCommand = NavigationCommand.FeatureCommand(
                featureName = intent.model.navigation.featureName,
                clearStack = intent.model.navigation.clearStack
            )
        )
    }

    private fun isTopLevelScreen(route: String): Boolean {
        return (HOME_TABS + BottomNavigationType.entries.map { it.featureName }).contains(route)
    }

    companion object {
        private val HOME_TABS =
            arrayOf("home","home/landing", "search/landing", "create/landing", "storeSetup/dfs", "messaging/landing", "profile/landing")
    }
}
