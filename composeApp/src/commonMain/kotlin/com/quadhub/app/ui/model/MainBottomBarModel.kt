package com.quadhub.app.ui.model

import androidx.compose.runtime.Immutable
import com.quadhub.app.ui.model.BottomNavigationType.*
import com.quadhub.compose.ui.element.image.Icons
import com.quadhub.compose.ui.navigation.model.BottomBarModel
import com.quadhub.compose.ui.navigation.model.BottomBarModelIcons

@Immutable
internal data class MainBottomBarModel(
    val model: BottomBarModel,
    val navigation: BottomNavigationType
) {

    companion object {
        val Default = entries.map { it.toMainModel(it == HOME) }
        fun select(model: MainBottomBarModel) = entries.map { it.toMainModel(it == model.navigation) }
    }
}

internal enum class BottomNavigationType(val featureName: String, val clearStack: Boolean = true) {
    HOME("home"),
    SEARCH("search"),
    PROFILE("profile")
}

internal fun BottomNavigationType.getInactiveImage() = when (this) {
    HOME -> Icons.close
    SEARCH -> Icons.close
    PROFILE -> Icons.close
}

internal fun BottomNavigationType.getActiveImage() = when(this) {
    HOME -> Icons.close
    SEARCH -> Icons.close
    PROFILE -> Icons.close
}

internal fun BottomNavigationType.toMainModel(active: Boolean = false) =
    MainBottomBarModel(
        model = BottomBarModel(
            icon = BottomBarModelIcons(
                activeIcon = this.getActiveImage(),
                inactiveIcon = this.getInactiveImage()
            ),
            active = active
        ),
        navigation = this
    )