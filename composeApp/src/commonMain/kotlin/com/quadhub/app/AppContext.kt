package com.quadhub.app

import com.quadhub.core.log.ErrorLogger
import com.quadhub.core.network.CoreFileUploadConverter
import com.quadhub.core.sharedpref.AppSharedStorageProvider


internal expect class AppContext constructor(any: Any)

internal expect fun AppContext.buildSharedStorage(storageFileName: String): AppSharedStorageProvider
internal expect fun AppContext.buildErrorLogger(): ErrorLogger
internal expect fun AppContext.getContext(): Any
internal expect fun AppContext.buildFileConverter(): CoreFileUploadConverter