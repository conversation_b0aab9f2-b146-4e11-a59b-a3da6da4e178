package com.quadhub.app.di

import com.quadhub.app.AppContext
import com.quadhub.app.buildErrorLogger
import com.quadhub.app.buildFileConverter
import com.quadhub.app.buildSharedStorage
import com.quadhub.core.presentation.navigation.compose.NavigatorHost
import com.quadhub.app.ui.MainViewModel
import com.quadhub.app.ui.di.MainUiComponent
import com.quadhub.core.auth.OauthTokenProvider
import com.quadhub.feature.auth.ui.di.AuthModule
import com.quadhub.feature.auth.ui.di.create


internal object RootAppBuilder {

    fun build(appContext: AppContext, serverUrl: String): Pair<NavigatorHost.Root, MainViewModel> {
        val authTokenStorage = appContext.buildSharedStorage("elephant")
        val tokenProvider = OauthTokenProvider.create(authTokenStorage)

        val errorLogger = appContext.buildErrorLogger()

        val fileConverter = appContext.buildFileConverter()
        val authModule = AuthModule::class.create()

        val mainViewModel = MainUiComponent::class.create

        val appComponent =
            ComposeAppComponent::class.create()

        return appComponent.root to mainViewModel.viewModel

    }

}